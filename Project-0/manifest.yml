modules:
  jira:customField:
    - key: simple-requirements-field
      name: Simple Requirements
      description: Input field for simple requirements for LLM expansion.
      type: string
      readOnly: false # Ensure it's editable
      # Context provider to trigger generation when this field is edited
      context:
        resolver:
          function: field-context-provider
    - key: full-requirements-field
      name: Full Requirements
      description: Output field for LLM-generated full requirements with rich text formatting.
      type: string
      userCreatable: true
      views:
        edit:
          type: textarea
        renderer:
          type: native

  function:
    - key: field-context-provider # Context provider for simple requirements field
      handler: index.simpleRequirementsContextProvider
    - key: issue-updated-handler # New function for the trigger
      handler: index.simpleRequirementsUpdatedHandler
  trigger: # New trigger module
    - key: issue-updated-trigger
      function: issue-updated-handler
      events:
        - avi:jira:updated:issue

app:
  runtime:
    name: nodejs22.x
  id: ari:cloud:ecosystem::app/1855cc49-eb1c-46b8-b8d7-04e25dbd5aa8
permissions: # Added permissions section
  scopes:
    - read:jira-work # To read issue details and custom fields
    - write:jira-work # To update custom fields
  external:
    fetch:
      backend:
        - https://openrouter.ai # Allow calls to OpenRouter API
