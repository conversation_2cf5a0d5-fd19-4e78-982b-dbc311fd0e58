modules:
  jira:customField:
    - key: simple-requirements-field
      name: Simple Requirements
      description: Input field for simple requirements for LLM expansion.
      type: string
      readOnly: false # Ensure it's editable
    - key: full-requirements-field
      name: Full Requirements
      description: Output field for LLM-generated full requirements with rich text formatting.
      type: object # Use object type to support Atlassian Document Format (ADF)
      readOnly: false # Allow editing
      view:
        formatter:
          expression: "value.content ? value.content.map(node => node.content ? node.content.map(textNode => textNode.text || '').join('') : '').join('\\n') : ''"
      schema:
        type: object
        properties:
          version:
            type: number
            const: 1
          type:
            type: string
            const: doc
          content:
            type: array
            items:
              type: object

  function:
    - key: requirements-updated-handler # New function for the trigger
      handler: index.simpleRequirementsUpdatedHandler
  trigger: # New trigger module
    - key: requirements-updated-trigger
      function: requirements-updated-handler
      events:
        - avi:jira:updated:issue

app:
  runtime:
    name: nodejs22.x
  id: ari:cloud:ecosystem::app/1855cc49-eb1c-46b8-b8d7-04e25dbd5aa8
permissions: # Added permissions section
  scopes:
    - read:jira-work # To read issue details and custom fields
    - write:jira-work # To update custom fields
  external:
    fetch:
      backend:
        - https://openrouter.ai # Allow calls to OpenRouter API
