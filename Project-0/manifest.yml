modules:
  jira:customField:
    - key: simple-requirements-field
      name: Simple Requirements
      description: Input field for simple requirements for LLM expansion.
      type: string
      readOnly: false # Ensure it's editable
    - key: full-requirements-field
      name: Full Requirements
      description: Output field for LLM-generated full requirements.
      type: string # Use string type for simpler handling
      readOnly: false # Allow editing
  jira:issueAction:
    - key: project-0-hello-world-issue-action
      resource: main
      resolver:
        function: resolver
      render: native
      title: Project-0
  function:
    - key: resolver
      handler: index.resolver
    - key: issue-created-handler # New function for the trigger
      handler: index.issueCreatedHandler
  trigger: # New trigger module
    - key: issue-created-trigger
      function: issue-created-handler
      events:
        - avi:jira:created:issue
resources:
  - key: main
    path: src/frontend/index.jsx
app:
  runtime:
    name: nodejs22.x
  id: ari:cloud:ecosystem::app/1855cc49-eb1c-46b8-b8d7-04e25dbd5aa8
permissions: # Added permissions section
  scopes:
    - read:jira-work # To read issue details and custom fields
    - write:jira-work # To update custom fields
  external:
    fetch:
      backend:
        - https://openrouter.ai # Allow calls to OpenRouter API
