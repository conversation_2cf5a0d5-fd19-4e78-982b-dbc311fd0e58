import Resolver from '@forge/resolver';
import api, { route } from '@forge/api'; // Import necessary Forge APIs

const resolver = new Resolver();

resolver.define('getText', (req) => {
  console.log(req);
  return 'Hello, world!';
});

// Existing handler export for the resolver (likely used by issueAction)
export const resolverHandler = resolver.getDefinitions();

// --- New Handler for Issue Creation Trigger ---

// Helper to get custom field ID - uses API fallback since context may not have field info
const getCustomFieldId = async (fieldKey) => {
  try {
    // Query the API to get all fields and find our custom fields
    const fieldsResponse = await api.asApp().requestJira(route`/rest/api/3/field`);
    if (!fieldsResponse.ok) {
      console.error(`Failed to fetch fields: ${fieldsResponse.status} ${fieldsResponse.statusText}`);
      return null;
    }

    const fieldsData = await fieldsResponse.json();

    // Map field keys to field names
    const fieldNameMap = {
      'simple-requirements-field': 'Simple Requirements',
      'full-requirements-field': 'Full Requirements'
    };

    const targetFieldName = fieldNameMap[fieldKey];
    if (!targetFieldName) {
      console.error(`Unknown field key: ${fieldKey}`);
      return null;
    }

    // Find the field by name
    const foundField = fieldsData.find(f => f.name === targetFieldName);
    if (!foundField) {
      console.error(`Could not find custom field with name: ${targetFieldName}`);
      return null;
    }

    console.log(`Found field ${targetFieldName} with ID: ${foundField.id}, Type: ${foundField.schema?.type}, Custom: ${foundField.schema?.custom}`);
    return foundField;
  } catch (error) {
    console.error(`Error fetching custom field ID for ${fieldKey}:`, error);
    return null;
  }
};



export const issueCreatedHandler = async (event, context) => {
  console.log('Issue Created Event Received:', JSON.stringify(event));
  console.log('Context:', JSON.stringify(context));

  const issueId = event.issue.id;
  const issueKey = event.issue.key;
  const issueTypeId = event.issue.fields.issuetype.id; // Get issue type ID

  // 1. Check if it's a 'Story' (You might need to adjust the name or fetch the ID)
  // Fetch issue type details to compare names (more robust than hardcoding IDs)
  let issueTypeName = '';
  try {
    const issueTypeResponse = await api.asApp().requestJira(route`/rest/api/3/issuetype/${issueTypeId}`);
    if (!issueTypeResponse.ok) {
      console.error(`Failed to fetch issue type ${issueTypeId}: ${issueTypeResponse.status} ${issueTypeResponse.statusText}`);
      const errorBody = await issueTypeResponse.text();
      console.error("Error Body:", errorBody);
      return; // Stop processing if we can't verify issue type
    }
    const issueTypeData = await issueTypeResponse.json();
    issueTypeName = issueTypeData.name;
    console.log(`Issue Type Name: ${issueTypeName}`);
  } catch (error) {
    console.error("Error fetching issue type:", error);
    return; // Stop processing on error
  }

  if (issueTypeName !== 'Story') {
    console.log(`Issue ${issueKey} is not a Story (Type: ${issueTypeName}). Skipping.`);
    return;
  }

  console.log(`Processing Story ${issueKey} (ID: ${issueId}).`);

  // 2. Get OpenRouter API Key from Environment Variables
  const apiKey = process.env.SECRET_OPENROUTER_API_KEY;
  if (!apiKey) {
    console.error('OpenRouter API Key (SECRET_OPENROUTER_API_KEY) is not set in Forge environment variables. Ensure it was set using `forge variables set --encrypt SECRET_OPENROUTER_API_KEY`.');
    // Optionally update the issue to indicate the error
    // await api.asApp().requestJira(route`/rest/api/3/issue/${issueId}/comment`, {
    //   method: 'POST',
    //   headers: { 'Accept': 'application/json', 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ body: { type: 'doc', version: 1, content: [{ type: 'paragraph', content: [{ type: 'text', text: 'Error: OpenRouter API Key not configured.' }] }] } })
    // });
    return;
  }

  // 3. Get Custom Field information using API
  const simpleReqField = await getCustomFieldId('simple-requirements-field');
  const fullReqField = await getCustomFieldId('full-requirements-field');

  if (!simpleReqField || !fullReqField) {
     console.error(`Could not resolve custom field information. Simple: ${simpleReqField}, Full: ${fullReqField}`);
     // Add comment to issue?
     return;
  }
  console.log(`Field IDs - Simple: ${simpleReqField.id}, Full: ${fullReqField.id}`);
  console.log(`Field Types - Simple: ${simpleReqField.schema?.type}, Full: ${fullReqField.schema?.type}`);

  // 4. Read "Simple Requirements" field value
  let simpleRequirements = null;
  try {
    const issueDetailsResponse = await api.asApp().requestJira(route`/rest/api/3/issue/${issueId}?fields=${simpleReqField.id}`);
    if (!issueDetailsResponse.ok) {
        console.error(`Failed to fetch issue ${issueId} fields: ${issueDetailsResponse.status} ${issueDetailsResponse.statusText}`);
        const errorBody = await issueDetailsResponse.text();
        console.error("Error Body:", errorBody);
        return;
    }
    const issueData = await issueDetailsResponse.json();
    simpleRequirements = issueData.fields[simpleReqField.id];
    console.log(`Simple Requirements Input: ${simpleRequirements}`);

  } catch (error) {
      console.error("Error fetching simple requirements field:", error);
      return;
  }

  if (!simpleRequirements || typeof simpleRequirements !== 'string' || simpleRequirements.trim() === '') {
    console.log(`Issue ${issueKey} has no value in the 'Simple Requirements' field (${simpleReqField.id}). Skipping LLM call.`);
    return;
  }

  // 5. Call OpenRouter API with DeepSeek V3
  const prompt = `Expand the following simple requirements into detailed, actionable user story requirements:\n\n"${simpleRequirements}"\n\nFormat the output clearly.`;
  let fullRequirements = '';

  try {
    console.log("Calling OpenRouter API with DeepSeek V3...");
    const openRouterResponse = await api.fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'https://project0jiraplugin.atlassian.net', // Optional: for analytics
        'X-Title': 'Jira Requirements Expander', // Optional: for analytics
      },
      body: JSON.stringify({
        model: 'deepseek/deepseek-chat-v3-0324:free', // DeepSeek V3 0324 free model
        messages: [
          { role: 'system', content: 'You are a helpful assistant that expands requirements for Jira user stories.' },
          { role: 'user', content: prompt },
        ],
        temperature: 0.7, // Adjust creativity
        max_tokens: 1000, // Adjust response length limit
      }),
    });

    if (!openRouterResponse.ok) {
      const errorBody = await openRouterResponse.text();
      console.error(`OpenRouter API request failed: ${openRouterResponse.status} ${openRouterResponse.statusText}`);
      console.error('OpenRouter Error Body:', errorBody);
      // Add comment to issue?
      return;
    }

    const openRouterData = await openRouterResponse.json();

    if (openRouterData.choices && openRouterData.choices.length > 0 && openRouterData.choices[0].message) {
        fullRequirements = openRouterData.choices[0].message.content.trim();
        console.log('OpenRouter Response Received:', fullRequirements);
    } else {
        console.error('OpenRouter response format unexpected or empty.', JSON.stringify(openRouterData));
        // Add comment to issue?
        return;
    }

  } catch (error) {
    console.error('Error calling OpenRouter API:', error);
    // Add comment to issue?
    return;
  }

  // 6. Update "Full Requirements" field (Using standard API for editable string field)
  try {
    console.log(`Updating issue ${issueKey} field ${fullReqField.id} with string content...`);

    // Use the standard issue update API since the field is now editable and string type
    const updateResponse = await api.asApp().requestJira(route`/rest/api/3/issue/${issueId}`, {
      method: 'PUT',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fields: {
          [fullReqField.id]: fullRequirements // Simple string value
        },
      }),
    });

    if (!updateResponse.ok) {
      const errorBody = await updateResponse.text();
      console.error(`Failed to update issue ${issueKey}: ${updateResponse.status} ${updateResponse.statusText}`);
      console.error('Update Error Body:', errorBody);
    } else {
      console.log(`Successfully updated 'Full Requirements' field (${fullReqField.id}) for issue ${issueKey}.`);
    }
  } catch (error) {
    console.error('Error updating Jira issue:', error);
  }
};

// Correct export for the original resolver functions
export const handler = resolverHandler;

// The issueCreatedHandler is exported separately by name and referenced directly in manifest.yml
